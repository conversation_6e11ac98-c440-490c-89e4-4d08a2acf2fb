<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>8扇形方向控制器演示</title>
    <style>
        body {
            margin: 0;
            padding: 50px;
            background: #f5f5f5;
            font-family: Arial, sans-serif;
        }
        
        .demo-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 400px;
        }
        
        .control-panel {
            width: 230px;
            border-radius: 6px;
            padding: 32px 0 24px 0;
            background: #fff;
            box-shadow: 0px 2px 16px 1px rgba(0, 0, 0, 0.1);
        }
        
        .top {
            margin: 0 auto;
            width: 180px;
            height: 180px;
            border-radius: 50%;
            background: #e4e7ed;
            overflow: hidden;
            position: relative;
        }

        /* 扇形按钮通用样式 */
        .sector {
            position: absolute;
            width: 90px;
            height: 90px;
            cursor: pointer;
            background: #e4e7ed;
            transition: all 0.3s ease;
            transform-origin: bottom right;
        }

        .sector:hover {
            background: #007aff;
        }

        .sector:hover .arrow {
            border-color: #fff !important;
        }

        /* CSS三角形箭头 */
        .arrow {
            position: absolute;
            width: 0;
            height: 0;
            border: 8px solid #909399;
            transition: all 0.3s ease;
        }

        /* 8个扇形区域 - 精确的45度分割 */
        
        /* 上方扇形 (0度) */
        .sector-up {
            top: 0;
            left: 45px;
            transform: rotate(-22.5deg);
            clip-path: polygon(50% 100%, 6.7% 25%, 93.3% 25%);
        }
        .sector-up .arrow {
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%) rotate(22.5deg);
            border-top-color: #909399;
            border-bottom: none;
            border-left: none;
            border-right: none;
        }
        
        /* 右上扇形 (45度) */
        .sector-up-right {
            top: 0;
            right: 0;
            transform: rotate(22.5deg);
            clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
        }
        .sector-up-right .arrow {
            left: 15px;
            top: 50%;
            transform: translateY(-50%) rotate(-22.5deg);
            border-top-color: #909399;
            border-right-color: #909399;
            border-bottom: none;
            border-left: none;
        }
        
        /* 右方扇形 (90度) */
        .sector-right {
            top: 45px;
            right: 0;
            transform: rotate(67.5deg);
            clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
        }
        .sector-right .arrow {
            left: 15px;
            top: 50%;
            transform: translateY(-50%) rotate(-67.5deg);
            border-right-color: #909399;
            border-top: none;
            border-bottom: none;
            border-left: none;
        }
        
        /* 右下扇形 (135度) */
        .sector-down-right {
            bottom: 0;
            right: 0;
            transform: rotate(112.5deg);
            clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
        }
        .sector-down-right .arrow {
            left: 15px;
            top: 50%;
            transform: translateY(-50%) rotate(-112.5deg);
            border-bottom-color: #909399;
            border-right-color: #909399;
            border-top: none;
            border-left: none;
        }
        
        /* 下方扇形 (180度) */
        .sector-down {
            bottom: 0;
            left: 45px;
            transform: rotate(157.5deg);
            clip-path: polygon(50% 100%, 6.7% 25%, 93.3% 25%);
        }
        .sector-down .arrow {
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%) rotate(-157.5deg);
            border-bottom-color: #909399;
            border-top: none;
            border-left: none;
            border-right: none;
        }
        
        /* 左下扇形 (225度) */
        .sector-down-left {
            bottom: 0;
            left: 0;
            transform: rotate(202.5deg);
            clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
        }
        .sector-down-left .arrow {
            left: 15px;
            top: 50%;
            transform: translateY(-50%) rotate(-202.5deg);
            border-bottom-color: #909399;
            border-left-color: #909399;
            border-top: none;
            border-right: none;
        }
        
        /* 左方扇形 (270度) */
        .sector-left {
            top: 45px;
            left: 0;
            transform: rotate(247.5deg);
            clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
        }
        .sector-left .arrow {
            left: 15px;
            top: 50%;
            transform: translateY(-50%) rotate(-247.5deg);
            border-left-color: #909399;
            border-top: none;
            border-bottom: none;
            border-right: none;
        }
        
        /* 左上扇形 (315度) */
        .sector-up-left {
            top: 0;
            left: 0;
            transform: rotate(292.5deg);
            clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
        }
        .sector-up-left .arrow {
            left: 15px;
            top: 50%;
            transform: translateY(-50%) rotate(-292.5deg);
            border-top-color: #909399;
            border-left-color: #909399;
            border-bottom: none;
            border-right: none;
        }

        /* 中心圆形按钮 */
        .center {
            position: absolute;
            left: 50%;
            top: 50%;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            width: 76px;
            height: 76px;
            border: 4px solid #fff;
            background: #e4e7ed;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .center:hover {
            background: rgba(76, 132, 255, 0.2);
            color: #4c84ff;
        }

        .info {
            text-align: center;
            margin-top: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="control-panel">
            <div class="top">
                <!-- 8个扇形方向按钮 -->
                <div data-role="up" class="sector sector-up">
                    <div class="arrow"></div>
                </div>
                <div data-role="up-right" class="sector sector-up-right">
                    <div class="arrow"></div>
                </div>
                <div data-role="right" class="sector sector-right">
                    <div class="arrow"></div>
                </div>
                <div data-role="down-right" class="sector sector-down-right">
                    <div class="arrow"></div>
                </div>
                <div data-role="down" class="sector sector-down">
                    <div class="arrow"></div>
                </div>
                <div data-role="down-left" class="sector sector-down-left">
                    <div class="arrow"></div>
                </div>
                <div data-role="left" class="sector sector-left">
                    <div class="arrow"></div>
                </div>
                <div data-role="up-left" class="sector sector-up-left">
                    <div class="arrow"></div>
                </div>
                
                <!-- 中心圆形按钮 -->
                <div data-role="zz" class="center">停止</div>
            </div>
            <div class="info">
                <p>8扇形方向控制器演示</p>
                <p>鼠标悬停查看hover效果</p>
            </div>
        </div>
    </div>

    <script>
        // 添加点击事件监听
        document.querySelectorAll('.sector, .center').forEach(element => {
            element.addEventListener('click', function() {
                const role = this.getAttribute('data-role');
                console.log('点击了:', role);
                alert('点击了: ' + role);
            });
        });
    </script>
</body>
</html>
