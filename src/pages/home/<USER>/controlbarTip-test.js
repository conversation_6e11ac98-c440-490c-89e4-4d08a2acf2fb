import React, { useState } from "react";
import ReactDOM from "react-dom";
import styled from "styled-components";
import { Slider } from "antd";
import { CloseOutlined } from "@ant-design/icons";

function ControlBarTipTest() {
  const [step, setStep] = useState(4);
  const [position, setPosition] = useState(() => {
    const initL = document.body.clientWidth - 320;
    const initT = document.body.clientHeight / 2 - 200;
    return { l: initL, t: initT };
  });

  return (
    <Root
      style={{ left: position.l + "px", top: position.t + "px" }}
      className="container barTip cursor-move"
    >
      <div className="top">
        {/* 8个扇形方向按钮 */}
        <div data-role="up" data-step={step} className="sector sector-1">
          <div className="arrow"></div>
        </div>
        <div data-role="up-right" data-step={step} className="sector sector-2">
          <div className="arrow"></div>
        </div>
        <div data-role="right" data-step={step} className="sector sector-3">
          <div className="arrow"></div>
        </div>
        <div data-role="down-right" data-step={step} className="sector sector-4">
          <div className="arrow"></div>
        </div>
        <div data-role="down" data-step={step} className="sector sector-5">
          <div className="arrow"></div>
        </div>
        <div data-role="down-left" data-step={step} className="sector sector-6">
          <div className="arrow"></div>
        </div>
        <div data-role="left" data-step={step} className="sector sector-7">
          <div className="arrow"></div>
        </div>
        <div data-role="up-left" data-step={step} className="sector sector-8">
          <div className="arrow"></div>
        </div>
        
        {/* 中心圆形按钮 */}
        <div data-role="zz" data-step={step} className="center">
          <div className="mask">停止</div>
        </div>
      </div>
    </Root>
  );
}

const Root = styled.div`
  z-index: 100;
  position: fixed;
  width: 230px;
  border-radius: 6px;
  padding: 32px 0 24px 0;
  background: #fff;
  box-shadow: 0px 2px 16px 1px rgba(0, 0, 0, 0.1);
  
  .top {
    margin: 0 auto;
    width: 180px;
    height: 180px;
    border-radius: 50%;
    position: relative;
    background: #f0f0f0;
    overflow: hidden;

    /* 扇形按钮通用样式 */
    .sector {
      position: absolute;
      width: 90px;
      height: 90px;
      cursor: pointer;
      background: #e4e7ed;
      transition: all 0.3s ease;
      transform-origin: bottom right;

      &:hover {
        background: #007aff;
        .arrow {
          border-color: #fff;
        }
      }

      .arrow {
        position: absolute;
        width: 0;
        height: 0;
        border: 8px solid #909399;
        transition: all 0.3s ease;
      }
    }

    /* 8个扇形的具体定位 */
    .sector-1 { /* 上 */
      top: 0;
      left: 45px;
      transform: rotate(-22.5deg);
      clip-path: polygon(50% 100%, 6.7% 25%, 93.3% 25%);
      .arrow {
        bottom: 15px;
        left: 50%;
        transform: translateX(-50%) rotate(22.5deg);
        border-top-color: #909399;
        border-bottom: none;
        border-left: none;
        border-right: none;
      }
    }

    .sector-2 { /* 右上 */
      top: 0;
      right: 0;
      transform: rotate(22.5deg);
      clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
      .arrow {
        left: 15px;
        top: 50%;
        transform: translateY(-50%) rotate(-22.5deg);
        border-top-color: #909399;
        border-right-color: #909399;
        border-bottom: none;
        border-left: none;
      }
    }

    .sector-3 { /* 右 */
      top: 45px;
      right: 0;
      transform: rotate(67.5deg);
      clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
      .arrow {
        left: 15px;
        top: 50%;
        transform: translateY(-50%) rotate(-67.5deg);
        border-right-color: #909399;
        border-top: none;
        border-bottom: none;
        border-left: none;
      }
    }

    .sector-4 { /* 右下 */
      bottom: 0;
      right: 0;
      transform: rotate(112.5deg);
      clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
      .arrow {
        left: 15px;
        top: 50%;
        transform: translateY(-50%) rotate(-112.5deg);
        border-bottom-color: #909399;
        border-right-color: #909399;
        border-top: none;
        border-left: none;
      }
    }

    .sector-5 { /* 下 */
      bottom: 0;
      left: 45px;
      transform: rotate(157.5deg);
      clip-path: polygon(50% 100%, 6.7% 25%, 93.3% 25%);
      .arrow {
        bottom: 15px;
        left: 50%;
        transform: translateX(-50%) rotate(-157.5deg);
        border-bottom-color: #909399;
        border-top: none;
        border-left: none;
        border-right: none;
      }
    }

    .sector-6 { /* 左下 */
      bottom: 0;
      left: 0;
      transform: rotate(202.5deg);
      clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
      .arrow {
        left: 15px;
        top: 50%;
        transform: translateY(-50%) rotate(-202.5deg);
        border-bottom-color: #909399;
        border-left-color: #909399;
        border-top: none;
        border-right: none;
      }
    }

    .sector-7 { /* 左 */
      top: 45px;
      left: 0;
      transform: rotate(247.5deg);
      clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
      .arrow {
        left: 15px;
        top: 50%;
        transform: translateY(-50%) rotate(-247.5deg);
        border-left-color: #909399;
        border-top: none;
        border-bottom: none;
        border-right: none;
      }
    }

    .sector-8 { /* 左上 */
      top: 0;
      left: 0;
      transform: rotate(292.5deg);
      clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
      .arrow {
        left: 15px;
        top: 50%;
        transform: translateY(-50%) rotate(-292.5deg);
        border-top-color: #909399;
        border-left-color: #909399;
        border-bottom: none;
        border-right: none;
      }
    }

    .center {
      position: absolute;
      left: 50%;
      top: 50%;
      border-radius: 50%;
      transform: translate(-50%, -50%);
      width: 76px;
      height: 76px;
      border: 4px solid #fff;
      background: #e4e7ed;
      z-index: 10;
      
      .mask {
        cursor: pointer;
        position: absolute;
        background: #e4e7ed;
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #666;
        
        &:hover {
          background: rgba(76, 132, 255, 0.2);
          color: #4c84ff;
        }
      }
    }
  }
`;

export default ControlBarTipTest;
