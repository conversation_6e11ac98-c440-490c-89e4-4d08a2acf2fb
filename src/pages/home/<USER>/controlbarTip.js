import React, { useState } from "react";
import ReactDOM from "react-dom";
import styled from "styled-components";
import { Slider } from "antd";
import { CloseOutlined } from "@ant-design/icons";

function ControlBarTip() {
  const [step, setStep] = useState(4);
  const [position, setPosition] = useState(() => {
    const initL = document.body.clientWidth - 320;
    const initT = document.body.clientHeight / 2 - 200;
    return { l: initL, t: initT };
  });
  const mouseDown = (e) => {
    if (
      e.target.className.indexOf &&
      e.target.className.indexOf("barTip") !== -1
    ) {
      const elWidth = e.target.clientWidth;
      const elHeight = e.target.clientHeight;
      document.onmousemove = (ev) => {
        let left = ev.clientX - e.nativeEvent.offsetX;
        let top = ev.clientY - e.nativeEvent.offsetY;
        left = Math.max(0, left);
        left = Math.min(left, document.body.clientWidth - elWidth);
        top = Math.max(0, top);
        top = Math.min(top, document.body.clientHeight - elHeight);
        setPosition({
          l: left,
          t: top,
        });
      };
      document.onmouseup = () => {
        document.onmousemove = null;
        document.onmouseup = null;
      };
    }
  };
  return (
    <Root
      style={{ left: position.l + "px", top: position.t + "px" }}
      onMouseDown={(e) => mouseDown(e)}
      className="container barTip cursor-move"
    >
      <div
        data-role="close"
        data-step={step}
        className="absolute right-4 top-4 cursor-pointer"
      >
        <CloseOutlined
          className="pointer-events-none"
          style={{ color: "#8C8C8C", fontSize: 18 }}
        />
      </div>

      <div className="top">
        {/* 8个扇形方向按钮 */}
        <div data-role="up" data-step={step} className="sector sector-up">
          <div className="arrow arrow-up"></div>
        </div>
        <div data-role="up-right" data-step={step} className="sector sector-up-right">
          <div className="arrow arrow-up-right"></div>
        </div>
        <div data-role="right" data-step={step} className="sector sector-right">
          <div className="arrow arrow-right"></div>
        </div>
        <div data-role="down-right" data-step={step} className="sector sector-down-right">
          <div className="arrow arrow-down-right"></div>
        </div>
        <div data-role="down" data-step={step} className="sector sector-down">
          <div className="arrow arrow-down"></div>
        </div>
        <div data-role="down-left" data-step={step} className="sector sector-down-left">
          <div className="arrow arrow-down-left"></div>
        </div>
        <div data-role="left" data-step={step} className="sector sector-left">
          <div className="arrow arrow-left"></div>
        </div>
        <div data-role="up-left" data-step={step} className="sector sector-up-left">
          <div className="arrow arrow-up-left"></div>
        </div>

        {/* 中心圆形按钮保持不变 */}
        <div data-role="zz" data-step={step} className="zz">
          <div className="mask">
            <i className="iconfont iconyuan"></i>
          </div>
        </div>
      </div>
      <div className="bottom">
        <div data-role="-" data-step={step}>
          <i className="iconfont iconsuoxiao1"></i>
        </div>
        <div data-role="+" data-step={step}>
          <i className="iconfont iconfangda"></i>
        </div>
      </div>
      <div className="slider">
        <div style={{ color: "#000" }}>移动速度 {step}</div>
        <Slider
          value={step}
          onChange={(value) => setStep(value)}
          max={8}
          min={1}
        />
      </div>
    </Root>
  );
}

export function ControlBarTipPortal(props) {
  return ReactDOM.createPortal(
    props.children,
    document.getElementById("modal-test")
  );
}

const Root = styled.div`
  z-index: 100;
  position: fixed;
  width: 230px;
  border-radius: 6px;
  padding: 32px 0 24px 0;
  background: #fff;
  box-shadow: 0px 2px 16px 1px rgba(0, 0, 0, 0.1);
  .top {
    margin: 0 auto;
    width: 180px;
    height: 180px;
    border-radius: 50%;
    background: #e4e7ed;
    overflow: hidden;
    position: relative;

    /* 扇形按钮通用样式 */
    .sector {
      position: absolute;
      width: 90px;
      height: 90px;
      cursor: pointer;
      background: #e4e7ed;
      transition: all 0.3s ease;
      transform-origin: bottom right;

      &:hover {
        background: #007aff;
        .arrow {
          border-color: #fff !important;
        }
      }

      /* CSS三角形箭头 */
      .arrow {
        position: absolute;
        width: 0;
        height: 0;
        border: 8px solid #909399;
        transition: all 0.3s ease;
      }
    }
  }

  /* 8个扇形区域 - 精确的45度分割 */

  /* 上方扇形 (0度) */
  .sector-up {
    top: 0;
    left: 45px;
    transform: rotate(-22.5deg);
    clip-path: polygon(50% 100%, 6.7% 25%, 93.3% 25%);
    .arrow {
      bottom: 15px;
      left: 50%;
      transform: translateX(-50%) rotate(22.5deg);
      border-top-color: #909399;
      border-bottom: none;
      border-left: none;
      border-right: none;
    }
  }

  /* 右上扇形 (45度) */
  .sector-up-right {
    top: 0;
    right: 0;
    transform: rotate(22.5deg);
    clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
    .arrow {
      left: 15px;
      top: 50%;
      transform: translateY(-50%) rotate(-22.5deg);
      border-top-color: #909399;
      border-right-color: #909399;
      border-bottom: none;
      border-left: none;
    }
  }

  /* 右方扇形 (90度) */
  .sector-right {
    top: 45px;
    right: 0;
    transform: rotate(67.5deg);
    clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
    .arrow {
      left: 15px;
      top: 50%;
      transform: translateY(-50%) rotate(-67.5deg);
      border-right-color: #909399;
      border-top: none;
      border-bottom: none;
      border-left: none;
    }
  }

  /* 右下扇形 (135度) */
  .sector-down-right {
    bottom: 0;
    right: 0;
    transform: rotate(112.5deg);
    clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
    .arrow {
      left: 15px;
      top: 50%;
      transform: translateY(-50%) rotate(-112.5deg);
      border-bottom-color: #909399;
      border-right-color: #909399;
      border-top: none;
      border-left: none;
    }
  }

  /* 下方扇形 (180度) */
  .sector-down {
    bottom: 0;
    left: 45px;
    transform: rotate(157.5deg);
    clip-path: polygon(50% 100%, 6.7% 25%, 93.3% 25%);
    .arrow {
      bottom: 15px;
      left: 50%;
      transform: translateX(-50%) rotate(-157.5deg);
      border-bottom-color: #909399;
      border-top: none;
      border-left: none;
      border-right: none;
    }
  }

  /* 左下扇形 (225度) */
  .sector-down-left {
    bottom: 0;
    left: 0;
    transform: rotate(202.5deg);
    clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
    .arrow {
      left: 15px;
      top: 50%;
      transform: translateY(-50%) rotate(-202.5deg);
      border-bottom-color: #909399;
      border-left-color: #909399;
      border-top: none;
      border-right: none;
    }
  }

  /* 左方扇形 (270度) */
  .sector-left {
    top: 45px;
    left: 0;
    transform: rotate(247.5deg);
    clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
    .arrow {
      left: 15px;
      top: 50%;
      transform: translateY(-50%) rotate(-247.5deg);
      border-left-color: #909399;
      border-top: none;
      border-bottom: none;
      border-right: none;
    }
  }

  /* 左上扇形 (315度) */
  .sector-up-left {
    top: 0;
    left: 0;
    transform: rotate(292.5deg);
    clip-path: polygon(0% 100%, 25% 6.7%, 25% 93.3%);
    .arrow {
      left: 15px;
      top: 50%;
      transform: translateY(-50%) rotate(-292.5deg);
      border-top-color: #909399;
      border-left-color: #909399;
      border-bottom: none;
      border-right: none;
    }
  }
  .zz {
    position: absolute;
    left: 50%;
    top: 50%;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    width: 76px;
    height: 76px;
    border: 4px solid #fff;
    background: #e4e7ed;
    z-index: 9;
    overflow: hidden;
    clip-path: circle(50% at 50% 50%);
    .mask {
      cursor: pointer;
      position: absolute;
      background: #e4e7ed;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      z-index: 10;
      &:hover {
        background: rgba(76, 132, 255, 0.2);
        i {
          color: #4c84ff;
        }
      }
    }
    i {
      position: absolute;
      left: 50%;
      top: 50%;
      font-size: 20px;
      z-index: 10;
      transform: translate(-50%, -50%);
      color: #a9acb1;
    }
  }

  .bottom {
    margin: 0 auto;
    width: 180px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
    div {
      cursor: pointer;
      text-align: center;
      line-height: 36px;
      width: 88px;
      height: 36px;
      background: #e4e7ed;
      &:hover {
        background: rgba(76, 132, 255, 0.2);
        i {
          color: #4c84ff;
        }
      }
      i {
        pointer-events: none;
        font-size: 20px;
        color: #595959;
      }
      &:first-child {
        border-radius: 18px 0px 0px 18px;
      }
      &:last-child {
        border-radius: 0px 18px 18px 0px;
      }
    }
  }
  .slider {
    padding: 20px 25px 0;
    .ant-slider-track {
      background-color: #007aff;
    }
    .ant-slider-handle {
      border-color: #007aff;
    }
  }
`;
export default ControlBarTip;
// export default ControlBarTip
