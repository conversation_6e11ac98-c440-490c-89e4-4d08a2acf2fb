import React, { useState } from "react";
import ReactDOM from "react-dom";
import styled from "styled-components";
import { Slider } from "antd";
import { CloseOutlined } from "@ant-design/icons";

function ControlBarTip() {
  const [step, setStep] = useState(4);
  const [position, setPosition] = useState(() => {
    const initL = document.body.clientWidth - 320;
    const initT = document.body.clientHeight / 2 - 200;
    return { l: initL, t: initT };
  });
  const mouseDown = (e) => {
    if (
      e.target.className.indexOf &&
      e.target.className.indexOf("barTip") !== -1
    ) {
      const elWidth = e.target.clientWidth;
      const elHeight = e.target.clientHeight;
      document.onmousemove = (ev) => {
        let left = ev.clientX - e.nativeEvent.offsetX;
        let top = ev.clientY - e.nativeEvent.offsetY;
        left = Math.max(0, left);
        left = Math.min(left, document.body.clientWidth - elWidth);
        top = Math.max(0, top);
        top = Math.min(top, document.body.clientHeight - elHeight);
        setPosition({
          l: left,
          t: top,
        });
      };
      document.onmouseup = () => {
        document.onmousemove = null;
        document.onmouseup = null;
      };
    }
  };
  return (
    <Root
      style={{ left: position.l + "px", top: position.t + "px" }}
      onMouseDown={(e) => mouseDown(e)}
      className="container barTip cursor-move"
    >
      <div
        data-role="close"
        data-step={step}
        className="absolute right-4 top-4 cursor-pointer"
      >
        <CloseOutlined
          className="pointer-events-none"
          style={{ color: "#8C8C8C", fontSize: 18 }}
        />
      </div>

      <div className="top">
        {/* 8个扇形方向按钮 */}
        <div data-role="up" data-step={step} className="sector sector-up">
          <div className="arrow arrow-up"></div>
        </div>
        <div data-role="up-right" data-step={step} className="sector sector-up-right">
          <div className="arrow arrow-up-right"></div>
        </div>
        <div data-role="right" data-step={step} className="sector sector-right">
          <div className="arrow arrow-right"></div>
        </div>
        <div data-role="down-right" data-step={step} className="sector sector-down-right">
          <div className="arrow arrow-down-right"></div>
        </div>
        <div data-role="down" data-step={step} className="sector sector-down">
          <div className="arrow arrow-down"></div>
        </div>
        <div data-role="down-left" data-step={step} className="sector sector-down-left">
          <div className="arrow arrow-down-left"></div>
        </div>
        <div data-role="left" data-step={step} className="sector sector-left">
          <div className="arrow arrow-left"></div>
        </div>
        <div data-role="up-left" data-step={step} className="sector sector-up-left">
          <div className="arrow arrow-up-left"></div>
        </div>

        {/* 中心圆形按钮保持不变 */}
        <div data-role="zz" data-step={step} className="zz">
          <div className="mask">
            <i className="iconfont iconyuan"></i>
          </div>
        </div>
      </div>
      <div className="bottom">
        <div data-role="-" data-step={step}>
          <i className="iconfont iconsuoxiao1"></i>
        </div>
        <div data-role="+" data-step={step}>
          <i className="iconfont iconfangda"></i>
        </div>
      </div>
      <div className="slider">
        <div style={{ color: "#000" }}>移动速度 {step}</div>
        <Slider
          value={step}
          onChange={(value) => setStep(value)}
          max={8}
          min={1}
        />
      </div>
    </Root>
  );
}

export function ControlBarTipPortal(props) {
  return ReactDOM.createPortal(
    props.children,
    document.getElementById("modal-test")
  );
}

const Root = styled.div`
  z-index: 100;
  position: fixed;
  width: 230px;
  border-radius: 6px;
  padding: 32px 0 24px 0;
  background: #fff;
  box-shadow: 0px 2px 16px 1px rgba(0, 0, 0, 0.1);
  .top {
    margin: 0 auto;
    width: 180px;
    height: 180px;
    border-radius: 50%;
    background: #e4e7ed;
    overflow: hidden;
    position: relative;
    clip-path: circle(50% at 50% 50%);

    /* 扇形按钮通用样式 */
    .sector {
      position: absolute;
      width: 90px;
      height: 90px;
      cursor: pointer;
      background: #e4e7ed;
      transition: background-color 0.3s ease;

      &:hover {
        background: #007aff;
        .arrow {
          border-bottom-color: #fff;
        }
      }

      /* CSS三角形箭头 */
      .arrow {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 0;
        height: 0;
        border: 8px solid transparent;
        border-bottom-color: #909399;
        transition: all 0.3s ease;
      }
    }
  }

  /* 8个扇形区域的具体定位和形状 */
  .sector-up {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    clip-path: polygon(50% 50%, 14.64% 14.64%, 50% 0%, 85.36% 14.64%);
    .arrow {
      top: 30%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .sector-up-right {
    top: 0;
    right: 0;
    clip-path: polygon(50% 50%, 85.36% 14.64%, 100% 0%, 100% 50%);
    .arrow {
      top: 30%;
      right: 30%;
      transform: translate(50%, -50%) rotate(45deg);
    }
  }

  .sector-right {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    clip-path: polygon(50% 50%, 85.36% 14.64%, 100% 50%, 85.36% 85.36%);
    .arrow {
      right: 30%;
      top: 50%;
      transform: translate(50%, -50%) rotate(90deg);
    }
  }

  .sector-down-right {
    bottom: 0;
    right: 0;
    clip-path: polygon(50% 50%, 85.36% 85.36%, 100% 100%, 100% 50%);
    .arrow {
      bottom: 30%;
      right: 30%;
      transform: translate(50%, 50%) rotate(135deg);
    }
  }

  .sector-down {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    clip-path: polygon(50% 50%, 85.36% 85.36%, 50% 100%, 14.64% 85.36%);
    .arrow {
      bottom: 30%;
      left: 50%;
      transform: translate(-50%, 50%) rotate(180deg);
    }
  }

  .sector-down-left {
    bottom: 0;
    left: 0;
    clip-path: polygon(50% 50%, 14.64% 85.36%, 0% 100%, 0% 50%);
    .arrow {
      bottom: 30%;
      left: 30%;
      transform: translate(-50%, 50%) rotate(225deg);
    }
  }

  .sector-left {
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    clip-path: polygon(50% 50%, 14.64% 85.36%, 0% 50%, 14.64% 14.64%);
    .arrow {
      left: 30%;
      top: 50%;
      transform: translate(-50%, -50%) rotate(270deg);
    }
  }

  .sector-up-left {
    top: 0;
    left: 0;
    clip-path: polygon(50% 50%, 14.64% 14.64%, 0% 0%, 0% 50%);
    .arrow {
      top: 30%;
      left: 30%;
      transform: translate(-50%, -50%) rotate(315deg);
    }
  }
  .zz {
    position: absolute;
    left: 50%;
    top: 50%;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    width: 76px;
    height: 76px;
    border: 4px solid #fff;
    background: #e4e7ed;
    z-index: 9;
    overflow: hidden;
    clip-path: circle(50% at 50% 50%);
    .mask {
      cursor: pointer;
      position: absolute;
      background: #e4e7ed;
      left: 0;
      top: 0;
      bottom: 0;
      right: 0;
      z-index: 10;
      &:hover {
        background: rgba(76, 132, 255, 0.2);
        i {
          color: #4c84ff;
        }
      }
    }
    i {
      position: absolute;
      left: 50%;
      top: 50%;
      font-size: 20px;
      z-index: 10;
      transform: translate(-50%, -50%);
      color: #a9acb1;
    }
  }

  .bottom {
    margin: 0 auto;
    width: 180px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
    div {
      cursor: pointer;
      text-align: center;
      line-height: 36px;
      width: 88px;
      height: 36px;
      background: #e4e7ed;
      &:hover {
        background: rgba(76, 132, 255, 0.2);
        i {
          color: #4c84ff;
        }
      }
      i {
        pointer-events: none;
        font-size: 20px;
        color: #595959;
      }
      &:first-child {
        border-radius: 18px 0px 0px 18px;
      }
      &:last-child {
        border-radius: 0px 18px 18px 0px;
      }
    }
  }
  .slider {
    padding: 20px 25px 0;
    .ant-slider-track {
      background-color: #007aff;
    }
    .ant-slider-handle {
      border-color: #007aff;
    }
  }
`;
export default ControlBarTip;
// export default ControlBarTip
